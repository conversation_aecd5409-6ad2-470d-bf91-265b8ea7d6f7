# Nostra - Fashion E-commerce Website

A modern, responsive fashion e-commerce website built with HTML, CSS (Tailwind), and JavaScript. Nostra offers a sleek shopping experience with dynamic product filtering, image sliders, and mobile-responsive design.

## 🌟 Features

### Homepage
- **Dynamic Banner Slider**: Auto-advancing image carousel with navigation controls and indicators
- **Brand Showcase**: Display of popular fashion brands (Puma, Adidas, etc.)
- **New Arrivals Section**: Latest fashion items with hover effects
- **Most Wanted Products**: Horizontal scrolling product gallery with sale badges
- **Customer Experience Cards**: Highlighting key benefits (Original Products, Satisfaction, Fast Shipping)
- **Newsletter Signup**: Email subscription for exclusive offers
- **Promotional Banner**: Dismissible top banner with special offers

### Collections Page
- **Advanced Filtering System**: Filter products by:
  - Occasion (Beach, Party, Summer)
  - Colors (White, Blue, Green, Red)
  - Arrivals (New, Old)
- **Real-time Search**: Instant product search with debounced input
- **Product Grid**: Responsive grid layout with hover animations
- **Interactive UI**: Smooth transitions and visual feedback

### Responsive Design
- **Mobile-First Approach**: Optimized for all screen sizes
- **Collapsible Navigation**: Mobile hamburger menu with smooth animations
- **Adaptive Layouts**: Grid systems that adjust to screen size
- **Touch-Friendly**: Optimized for mobile interactions

## 🛠️ Technologies Used

- **HTML5**: Semantic markup structure
- **CSS3**: Custom styling and animations
- **Tailwind CSS**: Utility-first CSS framework for rapid styling
- **JavaScript (ES6+)**: Interactive functionality and DOM manipulation
- **Alpine.js**: Lightweight JavaScript framework for reactive components

## 📁 Project Structure

```
nostra/
├── index.html              # Homepage with banner slider and product showcase
├── collections.html        # Product collections with filtering system
├── index.js                # Main JavaScript functionality
├── collections.js          # Collections page specific JavaScript
├── README.md               # Project documentation
└── images/                 # Image assets
    ├── slider-one.jpg      # Banner slider images
    ├── summer.jpg
    ├── party.jpg
    ├── ban2.jpg
    ├── n1.jpg - n4.jpg     # New arrivals products
    ├── m1.jpg - m8.jpg     # Most wanted products
    ├── brand logos/        # Brand logo images
    └── navigation/         # UI icons and symbols
```

## 🚀 Getting Started

### Prerequisites
- A modern web browser (Chrome, Firefox, Safari, Edge)
- Basic understanding of HTML, CSS, and JavaScript (for development)

### Installation & Setup

1. **Clone or Download the Repository**
   ```bash
   git clone <repository-url>
   cd nostra
   ```

2. **Open in Browser**
   - Simply open `index.html` in your web browser
   - Or use a local development server:
   ```bash
   # Using Python
   python -m http.server 8000

   # Using Node.js (if you have live-server installed)
   npx live-server
   ```

3. **Access the Website**
   - Homepage: `index.html`
   - Collections: `collections.html`

## 📱 Pages Overview

### Homepage (`index.html`)
The main landing page featuring:
- Hero banner with rotating promotional slides
- Brand partnerships section
- Featured product categories
- Customer value propositions
- Newsletter subscription

### Collections (`collections.html`)
Product catalog page with:
- Sidebar filtering system
- Search functionality
- Product grid with detailed information
- Responsive product cards with hover effects

## 🎨 Design Features

### Visual Elements
- **Color Scheme**: Modern grayscale with accent colors
- **Typography**: Cambria font family for elegant readability
- **Animations**: Smooth hover effects and transitions
- **Icons**: SVG icons for scalability and performance

### User Experience
- **Smooth Scrolling**: Navigation links scroll smoothly to sections
- **Loading States**: Visual feedback for user interactions
- **Error Handling**: Graceful handling of missing elements
- **Accessibility**: Semantic HTML and keyboard navigation support

## 🔧 JavaScript Functionality

### Core Features
- **Banner Slider**: Automatic and manual slide navigation
- **Mobile Menu**: Collapsible navigation for mobile devices
- **Product Filtering**: Real-time filtering with multiple criteria
- **Search**: Debounced search with instant results
- **Smooth Scrolling**: Enhanced navigation experience

### Code Organization
- Modular JavaScript with clear separation of concerns
- Event-driven architecture
- Responsive design utilities
- Performance optimizations (debouncing, efficient DOM queries)

## 📱 Responsive Breakpoints

- **Mobile**: < 768px
- **Tablet**: 768px - 1024px
- **Desktop**: > 1024px

Each breakpoint includes optimized:
- Grid layouts
- Typography scaling
- Navigation patterns
- Image sizing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-feature`)
3. Commit your changes (`git commit -am 'Add new feature'`)
4. Push to the branch (`git push origin feature/new-feature`)
5. Create a Pull Request

### Development Guidelines
- Follow existing code style and structure
- Test on multiple devices and browsers
- Optimize images before adding to the project
- Ensure accessibility standards are maintained

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- Tailwind CSS for the utility-first CSS framework
- Alpine.js for lightweight JavaScript reactivity
- Font families and icon libraries used in the project

---

**Nostra** - Elevating your style with modern fashion e-commerce experience.